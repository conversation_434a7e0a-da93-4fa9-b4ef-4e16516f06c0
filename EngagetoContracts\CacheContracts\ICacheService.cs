using EngagetoEntities.Entities;

namespace EngagetoContracts.CacheContracts
{
    public interface ICacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task<T?> GetAsync<T>(string key, Dictionary<string, object> searchCriteria) where T : class;
        Task SetAsync<T>(string key, T value) where T : class;
        Task<bool> ExistsAsync(string key);
        Task RemoveAsync(string key);
 
    }
}
