using EngagetoEntities.Entities;
using EngagetoDapper.Data.Interfaces.GenericInterfaces;
using EngagetoContracts.CacheContracts;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;

namespace EngagetoRepository.Services
{
    public class CacheService : ICacheService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<CacheService> _logger;
        private readonly IConfiguration _configuration;
        public CacheService(
            IServiceProvider serviceProvider,
            IMemoryCache memoryCache,
            IConfiguration configuration,
            ILogger<CacheService> logger)
        {
            _serviceProvider = serviceProvider;
            _memoryCache = memoryCache;
            _logger = logger;
            _configuration = configuration;
        }
        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return null;
            }

            try
            {
                if (_memoryCache.TryGetValue(key, out T? cachedValue))
                {
                    return cachedValue;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return null;
            }
        }

        public async Task<T?> GetAsync<T>(string key, Dictionary<string, object> searchCriteria) where T : class
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return null;
            }

            try
            {
                if (_memoryCache.TryGetValue(key, out T? cachedValue))
                {
                    return cachedValue;
                }

                // If not in cache, try to get from database using search criteria
                using var scope = _serviceProvider.CreateScope();
                var genericRepository = scope.ServiceProvider.GetRequiredService<IGenericRepository>();

                var entities = await genericRepository.GetByObjectAsync<T>(searchCriteria);
                var entity = entities?.FirstOrDefault();

                if (entity != null)
                {
                    _memoryCache.Set(key, entity, CreateCacheOptions());
                }

                return entity;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key} with search criteria", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value) where T : class
        {
            if (string.IsNullOrWhiteSpace(key) || value == null)
            {
                return;
            }

            try
            {
                _memoryCache.Set(key, value, CreateCacheOptions());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache for key: {Key}", key);
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return false;
            }

            try
            {
                return _memoryCache.TryGetValue(key, out _);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if key exists: {Key}", key);
                return false;
            }
        }

        public async Task RemoveAsync(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return;
            }

            try
            {
                _memoryCache.Remove(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache for key: {Key}", key);
            }
        }
        private MemoryCacheEntryOptions CreateCacheOptions()
        {
            var cacheExpirationMinutes = _configuration.GetValue<int>("CacheSettings:CacheExpirationTime");
            var _cacheExpiration = TimeSpan.FromMinutes(cacheExpirationMinutes);
            return new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(_cacheExpiration)
                .SetPriority(CacheItemPriority.High);
        }

    }
}
